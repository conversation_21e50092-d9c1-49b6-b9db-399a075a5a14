#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os.path
import sys
from PySide6.QtCore import Qt, QPoint, Signal, QSize, QRect, QEvent
from PySide6.QtGui import QColor, QPalette, QFont, QIcon, QMouseEvent, QCursor, QPainter, QPixmap
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QHBoxLayout, QVBoxLayout,
    QLabel, QPushButton, QTabBar, QSizePolicy, QFrame
)

from src.utils.resource_helper import get_image_path


class TabButton(QPushButton):

    def __init__(self, img_pth, parent=None):
        super().__init__(parent)

        self.setFixedSize(28, 28)
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        self.setFlat(True)
        
        # 设置样式
        self.setStyleSheet("""
            QPushButton {
                border: none;
                color: white;
                background-color: transparent;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 30);
            }
            QPushButton:pressed {
                background-color: rgba(255, 255, 255, 20);
            }
        """)

        if os.path.exists(img_pth):
            self.setIcon(QPixmap(img_pth))
            self.setIconSize(QSize(24, 24))


class TabCheckButton(QPushButton):
    """自定义标签按钮，支持选中状态和自绘制黄点"""
    
    def __init__(self, title, parent=None):
        super().__init__(title, parent)
        self.setMinimumSize(280, 70)
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        self.setCheckable(True)
        self.setStyleSheet("""
            QPushButton {
                background-color: #242425;
                color: #b1b1b1;
                border: none;
                border-radius: 8px;
                padding-left: 40px;
                padding-right: 30px;
                text-align: left;
                font-family: 'Alibaba PuHuiTi 2.0';
                font-size: 20px;
                font-weight: 300;
            }
            QPushButton:hover {
                color: #dddddd;
            }
            QPushButton:checked {
                color: white;
            }
        """)
        
    def paintEvent(self, event):
        # 先调用父类的绘制方法
        super().paintEvent(event)
        
        # 如果按钮被选中，绘制黄色圆点
        if self.isChecked():
            painter = QPainter(self)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)
            
            # 设置黄点颜色
            painter.setBrush(QColor("#FF9429"))
            painter.setPen(Qt.NoPen)
            
            # 绘制黄点
            dot_size = 12
            dot_x = 24 - dot_size // 2  # 距离按钮左边缘24像素的位置
            dot_y = self.height() // 2 - dot_size // 2
            painter.drawEllipse(dot_x, dot_y, dot_size, dot_size)


class CustomTitleBar(QWidget):

    # 定义信号
    windowClosed = Signal()
    windowMinimized = Signal()
    windowMaximized = Signal()
    tabClosed = Signal(int)  # 标签关闭信号
    tabChanged = Signal(int)  # 标签切换信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.setFixedHeight(80)
        self.setAutoFillBackground(True)
        
        # 设置背景颜色
        palette = self.palette()
        palette.setColor(QPalette.ColorRole.Window, QColor("#2D3234"))
        self.setPalette(palette)
        
        # 初始化UI
        self.init_ui()
        
        # 用于窗口拖动
        self.start_pos = None
        self.is_moving = False
        
        # 存储标签相关信息
        self.tabs = []  # 存储标签按钮
        self.tab_close_buttons = []  # 存储关闭按钮
        self.current_index = -1  # 当前选中的标签索引
        self.closable_tabs = set()  # 可关闭的标签索引集合
        
    def init_ui(self):
        layout = QHBoxLayout(self)
        layout.setContentsMargins(24, 7, 24, 7)
        layout.setSpacing(10)
        
        # 创建存放标签的容器
        self.tabs_container = QWidget(self)
        self.tabs_layout = QHBoxLayout(self.tabs_container)
        self.tabs_layout.setContentsMargins(0, 0, 0, 0)
        self.tabs_layout.setSpacing(5)
        self.tabs_layout.setAlignment(Qt.AlignLeft)
        
        # 创建窗口控制按钮
        btn_layout = QHBoxLayout()
        btn_layout.setSpacing(32)
        
        self.btn_min = TabButton(parent=self, img_pth=get_image_path("window_min.svg"))
        self.btn_min.setToolTip("最小化")
        self.btn_min.clicked.connect(self.windowMinimized.emit)
        
        self.btn_max = TabButton(parent=self, img_pth=get_image_path("window_max.svg"))
        self.btn_max.setToolTip("最大化")
        self.btn_max.clicked.connect(self.windowMaximized.emit)
        
        self.btn_close = TabButton(parent=self, img_pth=get_image_path("window_close.svg"))
        self.btn_close.setToolTip("关闭")
        self.btn_close.clicked.connect(self.windowClosed.emit)
        
        btn_layout.addWidget(self.btn_min)
        btn_layout.addWidget(self.btn_max)
        btn_layout.addWidget(self.btn_close)
        
        # 添加到主布局
        layout.addSpacing(205)
        layout.addWidget(self.tabs_container, 1)  # 让标签容器占据更多空间
        layout.addLayout(btn_layout)

    def create_tab_button(self, title, closable=True):
        # 创建标签按钮，使用新的TabCheckButton
        tab_button = TabCheckButton(title, self.tabs_container)
        
        index = len(self.tabs)
        tab_button.clicked.connect(lambda: self.select_tab(index))
        
        # 创建关闭按钮
        close_button = None
        if closable:
            close_button = QPushButton("×", self)
            close_button.setFixedSize(16, 16)
            close_button.setStyleSheet("""
                QPushButton {
                    border: none;
                    background-color: transparent;
                    color: #b1b1b1;
                    font-size: 14px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #ffcc00;
                    color: black;
                    border-radius: 8px;
                }
            """)
            close_button.setCursor(Qt.CursorShape.PointingHandCursor)
            close_button.clicked.connect(lambda: self.close_tab(index))
            close_button.raise_()  # 确保按钮在最上层
            self.closable_tabs.add(index)
            
        return tab_button, close_button
        
    def add_tab(self, title, closable=True):
        # 检查是否已存在同名标签
        for i, tab in enumerate(self.tabs):
            if tab.text() == title:
                self.select_tab(i)
                return i
                
        # 创建新标签
        tab_button, close_button = self.create_tab_button(title, closable)
        
        # 添加到布局
        self.tabs_layout.addWidget(tab_button)
        self.tabs.append(tab_button)
        self.tab_close_buttons.append(close_button)
        
        # 设置关闭按钮位置
        if close_button:
            close_button.setParent(self)
            close_button.show()  # 确保按钮是可见的
            close_button.raise_()
            
        # 默认选择新添加的标签
        index = len(self.tabs) - 1
        self.select_tab(index)
        
        # 更新所有关闭按钮位置
        QApplication.processEvents()
        self.update_all_close_buttons()
        
        return index
        
    def update_close_button_position(self, index):
        if index < len(self.tab_close_buttons) and self.tab_close_buttons[index]:
            button = self.tab_close_buttons[index]
            tab = self.tabs[index]
            # 获取标签在父窗口中的绝对位置
            tab_pos = tab.mapTo(self, QPoint(0, 0))
            button.move(tab_pos.x() + tab.width() - button.width() - 15, 
                       tab_pos.y() + tab.height() // 2 - button.height() // 2)
            button.show()  # 确保按钮可见
            button.raise_()  # 确保按钮在最上层
            
    def update_all_close_buttons(self):
        for i in range(len(self.tabs)):
            if i < len(self.tab_close_buttons) and self.tab_close_buttons[i]:
                self.update_close_button_position(i)
    
    def select_tab(self, index):
        if 0 <= index < len(self.tabs):
            # 取消当前选中的标签
            if 0 <= self.current_index < len(self.tabs):
                self.tabs[self.current_index].setChecked(False)
                
            # 选中新标签
            self.current_index = index
            self.tabs[index].setChecked(True)
            
            # 发出标签切换信号
            self.tabChanged.emit(index)
            
            # 更新UI和关闭按钮
            self.update()
            self.update_all_close_buttons()
    
    def switch_tab(self, title):
        for i, tab in enumerate(self.tabs):
            if tab.text() == title:
                self.select_tab(i)
                return
                
    def close_tab(self, index):
        if index not in self.closable_tabs or index >= len(self.tabs):
            return
            
        # 移除标签
        tab = self.tabs.pop(index)
        self.tabs_layout.removeWidget(tab)
        tab.deleteLater()
        
        # 移除关闭按钮
        close_button = self.tab_close_buttons.pop(index)
        if close_button:
            close_button.deleteLater()
            
        # 从可关闭集合中移除
        self.closable_tabs.remove(index)
        
        # 更新其他标签的索引
        new_closable_tabs = set()
        for i in self.closable_tabs:
            if i > index:
                new_closable_tabs.add(i - 1)
            else:
                new_closable_tabs.add(i)
        self.closable_tabs = new_closable_tabs
        
        # 更新选中的标签
        if self.tabs:
            new_index = min(index, len(self.tabs) - 1)
            self.current_index = -1  # 重置当前索引，确保select_tab会更新
            self.select_tab(new_index)
        else:
            self.current_index = -1
            
        # 更新按钮事件处理
        for i, tab in enumerate(self.tabs):
            tab.clicked.disconnect()
            tab.clicked.connect(lambda checked=False, idx=i: self.select_tab(idx))
            
            if i < len(self.tab_close_buttons) and self.tab_close_buttons[i]:
                self.tab_close_buttons[i].clicked.disconnect()
                self.tab_close_buttons[i].clicked.connect(lambda checked=False, idx=i: self.close_tab(idx))
        
        # 更新UI
        self.update_all_close_buttons()
        
        # 发出标签关闭信号
        self.tabClosed.emit(index)
        
    def resizeEvent(self, event):
        super().resizeEvent(event)
        # 延迟更新按钮位置，确保布局已经更新
        QApplication.processEvents()
        self.update_all_close_buttons()
        
    def showEvent(self, event):
        super().showEvent(event)
        # 窗口显示时更新所有关闭按钮位置
        QApplication.processEvents()
        self.update_all_close_buttons()
        
    def mousePressEvent(self, event: QMouseEvent):
        if event.button() == Qt.LeftButton:
            # 标记为正在拖动
            self.is_moving = True
            self.start_pos = event.globalPosition().toPoint()
            # 设置鼠标样式为移动状态
            self.setCursor(Qt.ClosedHandCursor)
            # 阻止事件传递给父窗口
            event.accept()
        else:
            super().mousePressEvent(event)
        
    def mouseMoveEvent(self, event: QMouseEvent):
        if self.is_moving and event.buttons() == Qt.LeftButton:
            # 如果窗口是最大化状态，不允许拖动
            if self.parent and not self.parent.isMaximized():
                delta = event.globalPosition().toPoint() - self.start_pos
                self.parent.move(self.parent.pos() + delta)
                self.start_pos = event.globalPosition().toPoint()
            # 阻止事件传递给父窗口
            event.accept()
        else:
            super().mouseMoveEvent(event)
        
    def mouseReleaseEvent(self, event: QMouseEvent):
        if self.is_moving:
            self.is_moving = False
            self.start_pos = None
            # 恢复鼠标样式
            self.setCursor(Qt.ArrowCursor)
            # 阻止事件传递给父窗口
            event.accept()
        else:
            super().mouseReleaseEvent(event)
        
    def mouseDoubleClickEvent(self, event: QMouseEvent):
        if event.button() == Qt.LeftButton:
            self.windowMaximized.emit()
            # 设置为非拖动状态，防止恢复窗口后立即触发拖动
            self.is_moving = False
            self.start_pos = None
            # 阻止事件传递给父窗口
            event.accept()
        else:
            super().mouseDoubleClickEvent(event)


class CustomFrame(QFrame):

    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 设置样式
        self.setStyleSheet("""
            CustomFrame {
                background-color: #252A2D;
                border: none;
            }
        """)


class BorderlessWindow(QMainWindow):

    # 定义调整大小的区域宽度
    BORDER_WIDTH = 5
    
    # 定义窗口调整方向
    DIRECTION_LEFT = 1
    DIRECTION_TOP = 2
    DIRECTION_RIGHT = 4
    DIRECTION_BOTTOM = 8
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 设置无边框窗口
        self.setWindowFlags(Qt.FramelessWindowHint)
        # self.setAttribute(Qt.WA_TranslucentBackground)  # 会导致urdf文件加载有问题
        
        # 创建中心部件
        self.central_widget = QWidget(self)
        self.setCentralWidget(self.central_widget)
        
        # 主布局
        self.main_layout = QVBoxLayout(self.central_widget)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        
        # 创建自定义标题栏
        self.title_bar = CustomTitleBar(self)
        self.title_bar.windowClosed.connect(self.close)
        self.title_bar.windowMinimized.connect(self.showMinimized)
        self.title_bar.windowMaximized.connect(self.toggle_maximize)
        
        # 创建内容区域
        self.content_frame = CustomFrame(self)
        self.content_layout = QVBoxLayout(self.content_frame)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        
        # 添加到主布局
        self.main_layout.addWidget(self.title_bar)
        self.main_layout.addWidget(self.content_frame, 1)
        
        # 初始窗口大小
        self.resize(800, 600)
        
        # 用于窗口大小调整
        self.direction = 0
        self.is_resizing = False
        self.start_pos = QPoint()
        self.start_geometry = QRect()
        
        # 保存最大化前的窗口位置和大小
        self.normal_geometry = None
        
        # 设置鼠标跟踪
        self.setMouseTracking(True)
        self.central_widget.setMouseTracking(True)
        self.content_frame.setMouseTracking(True)
        
    def toggle_maximize(self):
        if self.isMaximized() or self.isFullScreen():
            # 从最大化恢复到正常状态
            self.showNormal()
            
            # 如果有保存的几何信息，则恢复到之前的位置和大小
            if self.normal_geometry:
                self.setGeometry(self.normal_geometry)
            else:
                # 如果没有保存的几何信息，则使用鼠标位置定位
                cursor_pos = QCursor.pos()
                title_bar_height = self.title_bar.height()
                
                # 计算窗口左上角位置，使鼠标位于标题栏中心
                new_x = cursor_pos.x() - (self.width() / 2)
                new_y = cursor_pos.y() - (title_bar_height / 2)
                
                # 确保窗口不会超出屏幕
                screen_size = QApplication.primaryScreen().size()
                new_x = max(0, min(new_x, screen_size.width() - self.width()))
                new_y = max(0, min(new_y, screen_size.height() - self.height()))
                
                # 移动窗口
                self.move(new_x, new_y)
        else:
            # 保存当前窗口位置和大小，以便后续恢复
            self.normal_geometry = self.geometry()
            self.showMaximized()

    def add_tab(self, title, closable=True):
        return self.title_bar.add_tab(title, closable)

    def switch_tab(self, title):
        return self.title_bar.switch_tab(title)
    
    def set_content(self, widget):
        # 清除现有布局中的部件
        while self.content_layout.count():
            item = self.content_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()

        # 添加新部件
        self.content_layout.addWidget(widget)
        
        # 确保新部件也有鼠标跟踪
        widget.setMouseTracking(True)
    
    def get_direction(self, pos):
        rect = self.rect()
        x, y = pos.x(), pos.y()
        
        # 初始化方向
        direction = 0
        
        # 左边缘
        if x <= self.BORDER_WIDTH:
            direction |= self.DIRECTION_LEFT
            
        # 右边缘
        if x >= rect.width() - self.BORDER_WIDTH:
            direction |= self.DIRECTION_RIGHT
            
        # 上边缘
        if y <= self.BORDER_WIDTH:
            direction |= self.DIRECTION_TOP
            
        # 下边缘
        if y >= rect.height() - self.BORDER_WIDTH:
            direction |= self.DIRECTION_BOTTOM
            
        return direction
    
    def set_cursor_shape(self, direction):
        if direction == 0:
            self.setCursor(Qt.ArrowCursor)
        elif direction in (self.DIRECTION_LEFT, self.DIRECTION_RIGHT):
            self.setCursor(Qt.SizeHorCursor)
        elif direction in (self.DIRECTION_TOP, self.DIRECTION_BOTTOM):
            self.setCursor(Qt.SizeVerCursor)
        elif direction in (self.DIRECTION_TOP | self.DIRECTION_LEFT, 
                          self.DIRECTION_BOTTOM | self.DIRECTION_RIGHT):
            self.setCursor(Qt.SizeFDiagCursor)
        elif direction in (self.DIRECTION_TOP | self.DIRECTION_RIGHT, 
                          self.DIRECTION_BOTTOM | self.DIRECTION_LEFT):
            self.setCursor(Qt.SizeBDiagCursor)
    
    def mousePressEvent(self, event: QMouseEvent):
        if event.button() == Qt.LeftButton:
            # 获取当前鼠标位置的方向
            self.direction = self.get_direction(event.position().toPoint())
            
            # 只有在窗口边缘时才进行大小调整
            if self.direction != 0:
                self.is_resizing = True
                self.start_pos = event.globalPosition().toPoint()
                self.start_geometry = self.geometry()
        super().mousePressEvent(event)
    
    def mouseMoveEvent(self, event: QMouseEvent):
        # 如果窗口已最大化，则不进行大小调整
        if self.isMaximized():
            return
            
        pos = event.position().toPoint()
        
        # 如果正在调整大小
        if self.is_resizing and event.buttons() == Qt.LeftButton:
            delta = event.globalPosition().toPoint() - self.start_pos
            new_geometry = QRect(self.start_geometry)
            
            # 根据方向调整窗口大小
            if self.direction & self.DIRECTION_LEFT:
                new_geometry.setLeft(self.start_geometry.left() + delta.x())
            if self.direction & self.DIRECTION_RIGHT:
                new_geometry.setRight(self.start_geometry.right() + delta.x())
            if self.direction & self.DIRECTION_TOP:
                new_geometry.setTop(self.start_geometry.top() + delta.y())
            if self.direction & self.DIRECTION_BOTTOM:
                new_geometry.setBottom(self.start_geometry.bottom() + delta.y())
                
            # 设置最小大小限制
            if new_geometry.width() < self.minimumWidth():
                if self.direction & self.DIRECTION_LEFT:
                    new_geometry.setLeft(new_geometry.right() - self.minimumWidth())
                else:
                    new_geometry.setRight(new_geometry.left() + self.minimumWidth())
                    
            if new_geometry.height() < self.minimumHeight():
                if self.direction & self.DIRECTION_TOP:
                    new_geometry.setTop(new_geometry.bottom() - self.minimumHeight())
                else:
                    new_geometry.setBottom(new_geometry.top() + self.minimumHeight())
                    
            # 应用新几何形状
            self.setGeometry(new_geometry)
        else:
            # 只在非调整大小状态下更新鼠标形状
            if not self.is_resizing:
                # 获取鼠标所在的窗口边缘方向
                direction = self.get_direction(pos)
                # 设置鼠标形状
                self.set_cursor_shape(direction)
            
        super().mouseMoveEvent(event)
    
    def mouseReleaseEvent(self, event: QMouseEvent):
        self.is_resizing = False
        super().mouseReleaseEvent(event)
    
    def leaveEvent(self, event):
        # 只在非调整大小状态下重置鼠标形状
        if not self.is_resizing:
            self.setCursor(Qt.ArrowCursor)
        super().leaveEvent(event)
        
    def resizeEvent(self, event):
        super().resizeEvent(event)
        # 窗口大小变化时，更新标签栏中的关闭按钮位置
        QApplication.processEvents()
        self.title_bar.update_all_close_buttons()
        
    def showEvent(self, event):
        super().showEvent(event)
        # 窗口显示时，更新标签栏中的关闭按钮位置
        QApplication.processEvents()
        self.title_bar.update_all_close_buttons()
        
        # 如果是首次显示且不是最大化状态，保存初始几何信息
        if not self.isMaximized() and not self.normal_geometry:
            self.normal_geometry = self.geometry()

    def keyPressEvent(self, event):
        if event.key() == Qt.Key_Escape and self.isFullScreen():
            self.showNormal()
            if self.normal_geometry:
                self.setGeometry(self.normal_geometry)
        else:
            super().keyPressEvent(event)