#!/usr/bin/env python3
"""
测试VTK URDF查看器修复
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton
from PySide6.QtCore import Qt

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.gui.widgets.vtk_urdf_viewer.urdf_viewer import VTKURDFViewer


class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("VTK URDF查看器修复测试")
        self.setGeometry(100, 100, 1200, 800)

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        # 创建按钮
        load_button = QPushButton("加载URDF模型")
        load_button.clicked.connect(self.load_urdf)
        layout.addWidget(load_button)

        # 创建VTK查看器
        self.vtk_viewer = VTKURDFViewer()
        layout.addWidget(self.vtk_viewer)

        print("VTK URDF查看器已创建")

    def load_urdf(self):
        """加载URDF文件"""
        # 使用默认的URDF路径
        urdf_path = "C:/Users/<USER>/Downloads/URDF_demo/brainco-lefthand-URDF-V2/urdf/brainco-lefthand-URDF-V2_converted.urdf"
        if os.path.exists(urdf_path):
            print(f"开始加载URDF: {urdf_path}")
            self.vtk_viewer.load_urdf(urdf_path)
        else:
            print(f"URDF文件不存在: {urdf_path}")
            print("请检查文件路径是否正确")


def main():
    app = QApplication(sys.argv)
    
    window = TestWindow()
    window.show()
    
    print("测试窗口已显示")
    print("点击'加载URDF模型'按钮来测试VTK显示")
    print("如果修复成功，应该能看到3D手部模型而不是背景图像")
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
