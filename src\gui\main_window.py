import os
import sys
import time

from enum import IntEnum
import numpy as np

from PySide6.QtCore import <PERSON><PERSON><PERSON>, QTimer, QUrl, Qt, Signal, QPropertyAnimation, QEasingCurve
from PySide6.QtGui import QDesktopServices
from PySide6.QtWidgets import QStackedWidget, QLabel, QPushButton, QFrame, QWidget, QBoxLayout, QHBoxLayout, \
    QVBoxLayout, QProgressBar, QSizePolicy, QFileDialog, QButtonGroup, QRadioButton, QTabWidget, QScrollArea
from loguru import logger

from src.gui.widgets.gesture_card_widget import GestureCardWidget
from src.gui.components.radiobutton import CustomRadioButton
from src.gui.ui_config import ControlType
from src.config import OFFICIAL_WEBSITE_ADDRESS, DRAW_REFRESH_PERIOD
from src.core import HandVersion, Baudrate, ActionSequenceId
from src.core.hand_specifications import HandType
from src.gui.components.base_widgets import BorderlessLayout
from src.gui.components.borderless_main_window import BorderlessWindow
from src.gui.widgets.alert_wiidget import AlertWidget
from src.gui.widgets.device_connect.device_connect_widget import DeviceConnectWidget, DeviceConnectDialog
from src.gui.widgets.finger_data_display_widget import FingerDataDisplayWidget
from src.gui.widgets.hand_control_widget import HandControlWidget
from src.gui.widgets.init_overlay_widget import InitializationOverlayWidget
from src.gui.widgets.ota_download_view import OTADownloadDialog
from src.gui.widgets.settings_widget import SettingsPanelWidget
from src.gui.widgets.hand_sim_viz_widget import HandSimVizWidget
from src.utils.config_loader import get_config, save_config
from src.utils.resource_helper import get_image
from src.core.sdk_manager import SDKManager, HandConfig, stark, HandDevice


_FIRST_INIT_UI = True


class AppMainWindow(BorderlessWindow):

    def __init__(self):
        super().__init__()

        self._expected_hand_version = HandVersion.V2

        self._sdk_manager = SDKManager.instance()

        self.showFullScreen()

        # 绘图buffer
        self._max_plot_size = 1000
        
        # 初始化所有缓冲区
        self._buffers = {
            HandType.Left: {
                'status': None,
                'position': [np.array([], dtype=float) for _ in range(6)],
                'speed': [np.array([], dtype=float) for _ in range(6)],
                'current': [np.array([], dtype=float) for _ in range(6)]
            },
            HandType.Right: {
                'status': None,
                'position': [np.array([], dtype=float) for _ in range(6)],
                'speed': [np.array([], dtype=float) for _ in range(6)],
                'current': [np.array([], dtype=float) for _ in range(6)]
            }
        }

        self._plot_timer = QTimer()
        self._plot_timer.timeout.connect(self._start_plot_widget)
        
        # 初始化状态
        self._initialization_completed = False

        self._input_field_range = {HandType.Left: {}, HandType.Right: {}}

        self._init_ui()
        self._connect_event()
        
        # 确保初始显示连接界面
        self.main_stack_widget.setCurrentIndex(0)

    def _connect_event(self):
        self.title_bar.tabChanged.connect(self._on_tab_changed)
        self.title_bar.tabClosed.connect(self._on_tab_closed)  # 添加对tabClosed信号的连接

        # 连接SDK信号
        self._sdk_manager.connect_result_signal.connect(self._on_sdk_connect_result)
        self._sdk_manager.motor_status_received_signal.connect(self._on_sdk_motor_status_received)
        self._sdk_manager.error_occurred_signal.connect(self._on_sdk_error_occurred)
        self._sdk_manager.finger_unit_mode_received_signal.connect(self._on_sdk_finger_unit_mode_received)
        self._sdk_manager.finger_unit_mode_error_signal.connect(self._on_sdk_finger_unit_mode_error)
        self._sdk_manager.get_completed_device_info_signal.connect(self._on_sdk_get_completed_device_info)
        self._sdk_manager.initialization_task_completed_signal.connect(self._on_initialization_task_completed)
        self._sdk_manager.dfu_state_signal.connect(self._on_dfu_state_callback)
        self._sdk_manager.dfu_progress_signal.connect(self._on_dfu_progress_callback)
        self._sdk_manager.dfu_error_signal.connect(self._on_sdk_dfu_error)
        self._sdk_manager.dfu_finished_signal.connect(self._on_dfu_finished)
        self._sdk_manager.dfu_reconnect_signal.connect(self._on_dfu_reconnect)
        self._sdk_manager.dfu_started_signal.connect(lambda file_path: self.ota_dialog.show_dialog(os.path.basename(file_path)))
        self._sdk_manager.update_urdf_signal.connect(self._update_urdf_viewer)

        self.home_button.clicked.connect(self._on_home_button_clicked)
        self.device_connect_widget.connect_signal.connect(self._on_device_connect_signal)

        self.settings_button.clicked.connect(self._on_settings_button_clicked)
        self.settings_widget.device_name_lineedit.textChanged.connect(self._on_device_name_changed)
        self.settings_widget.upgrade_fw_widget.clicked.connect(self._on_upgrade_fw_widget_clicked)
        self.settings_widget.enable_auto_zeroing_widget.toggled.connect(lambda enabled: self._sdk_manager.set_position_auto_calibration(self.list_available_hand_types()[0], enabled))
        self.settings_widget.enabl_turbo_widget.toggled.connect(lambda enabled: self._sdk_manager.set_turbo_mode_enabled(self.list_available_hand_types()[0], enabled))
        self.settings_widget.enabl_led_widget.toggled.connect(lambda enabled: self._sdk_manager.set_led_enabled(self.list_available_hand_types()[0], enabled))
        self.settings_widget.enabl_buzzer_widget.toggled.connect(lambda enabled: self._sdk_manager.set_buzzer_enabled(self.list_available_hand_types()[0], enabled))
        self.settings_widget.enabl_vibration_widget.toggled.connect(lambda enabled: self._sdk_manager.set_vibration_enabled(self.list_available_hand_types()[0], enabled))
        self.settings_widget.pushButton_restore_factory.clicked.connect(lambda: self._sdk_manager.reset_default_settings(self.list_available_hand_types()[0]))

        self.hand_control_widget.slider_value_changed_signal.connect(self._on_slider_value_changed)
        self.hand_control_widget.apply_all_button.clicked.connect(self._on_apply_all_button_clicked)
        self.hand_control_widget.set_gesture_zero_button.clicked.connect(self._on_set_gesture_zero_button_clicked)
        self.hand_control_widget.set_packing_gestures_button.clicked.connect(self._on_set_packing_gestures_button_clicked)
        self.finger_data_display_widget.radioButton_percentage.clicked.connect(self._on_radio_button_percentage_clicked)
        self.finger_data_display_widget.radioButton_number.clicked.connect(self._on_radio_button_percentage_clicked)
        self.hand_sim_viz_widget.switch_hand_type_signal.connect(self._on_change_hand_type)
        self.hand_sim_viz_widget.pushButton_connect_left.clicked.connect(lambda: self._on_urdf_connect_button_clicked(HandType.Left))
        self.hand_sim_viz_widget.pushButton_connect_right.clicked.connect(lambda: self._on_urdf_connect_button_clicked(HandType.Right))

        # 连接初始化完成信号
        self.initialization_overlay.initializationCompleted.connect(self._on_initialization_completed)

    def _init_ui(self):

        # ota
        self.ota_dialog = OTADownloadDialog(self)

        # 添加主页按钮
        self.home_button = QPushButton("")
        self.home_button.setAutoFillBackground(True)
        self.home_button.setStyleSheet("background-color: #242425; border: none; border-radius: 8px;")
        self.home_button.setFixedSize(60, 60)
        self.home_button.setIconSize(QSize(60, 60))
        self.home_button.setIcon(get_image("home.svg"))
        self.title_bar.layout().insertWidget(0, self.home_button)

        frame = QFrame()
        frame.setStyleSheet("background-color: gray;")
        frame.setFixedWidth(1)
        self.title_bar.layout().insertWidget(0, frame)

        # 添加logo
        self.logo_label = QLabel()
        self.logo_label.setFixedSize(160, 28)
        self.logo_label.setScaledContents(True)
        self.logo_label.setPixmap(get_image("app_logo.png"))
        self.title_bar.layout().insertWidget(0, self.logo_label)

        # 切换界面
        self.main_stack_widget = QStackedWidget()
        self.main_stack_widget.setStyleSheet("background-color: #22262E;")
        # self.main_stack_widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.set_content(self.main_stack_widget)

        # 主连接界面
        self.device_connect_widget = DeviceConnectWidget()
        container = QWidget()
        container_layout = BorderlessLayout(QBoxLayout.Direction.LeftToRight)
        container.setLayout(container_layout)
        container_layout.addWidget(self.device_connect_widget)
        self.main_stack_widget.addWidget(container)

        # Hand Control界面
        hand_sim_viz_parent_widget = QFrame()
        hand_sim_viz_parent_widget.setLayout(BorderlessLayout(QBoxLayout.Direction.TopToBottom))
        self.hand_sim_viz_widget = HandSimVizWidget(hand_sim_viz_parent_widget)
        self.hand_sim_viz_widget.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Expanding)
        self.hand_sim_viz_widget.setFixedWidth(600)
        self.settings_button = QPushButton(self.tr("Settings"))
        # self.settings_button.setLayout(QHBoxLayout())
        # self.settings_button.layout().setSpacing(15)
        self.settings_button.setIcon(get_image("settings.svg"))
        self.settings_button.setIconSize(QSize(24, 24))
        self.settings_button.setFixedHeight(80)
        self.settings_button.setStyleSheet("""
font-family: 'Alibaba PuHuiTi 2.0';
color: white;
background-color: #2E3034;
border: none;
border-radius: 8px;
font-size: 20px;
font-weight: 300;
text-align: left;
padding-left: 20px;
letter-spacing: 0px;  /* 可能不支持，依赖平台 */
""")
        hand_sim_viz_parent_widget.layout().addWidget(self.hand_sim_viz_widget)
        hand_sim_viz_parent_widget.layout().addWidget(self.settings_button)
        self.control_widget = QFrame()
        self.control_widget.setLayout(BorderlessLayout(QBoxLayout.Direction.TopToBottom))
        self.control_widget.layout().setSpacing(15)
        self.finger_data_display_widget = FingerDataDisplayWidget()
        self.hand_control_widget = HandControlWidget()
        h_layout = QHBoxLayout()
        h_layout.setSpacing(40)
        h_layout.setContentsMargins(15, 9, 9, 9)

        self.control_type_button_group = QButtonGroup(self)

        for idx, control_type in enumerate(ControlType):
            name = " && ".join(word.capitalize() for word in control_type.name.split("_")) + " Control"

            button = CustomRadioButton(name, self.control_widget)
            button.setProperty("control_type", control_type)

            self.control_type_button_group.addButton(button, idx)
            h_layout.addWidget(button)

            if control_type == ControlType.POSITION:
                button.setChecked(True)
                self.hand_control_widget.set_finger_controls_enabled_by_typed(control_type)

        h_layout.addStretch()
        self.control_type_button_group.buttonClicked.connect(self.on_control_type_button_clicked)

        self.control_widget.layout().addWidget(self.finger_data_display_widget)
        self.control_widget.layout().addLayout(h_layout)
        self.control_widget.layout().addWidget(self.hand_control_widget)

        # 功能区切换界面
        self.functional_stack_widget = QStackedWidget()
        self.functional_stack_widget.setStyleSheet("background-color: #22262E;")
        self.functional_stack_widget.addWidget(self.control_widget)

        # 手势设置界面
        self.gesture_tab_widget = QTabWidget()
        self.gesture_tab_widget.tabBar().hide()
        self.gesture_tab_widget.setStyleSheet("border: none;")
        for hand_type in HandType:
            gesture_widget = GestureCardWidget(hand_type)
            self.gesture_tab_widget.addTab(gesture_widget, hand_type.name)
            gesture_widget.download_signal.connect(self._on_gesture_download_signal)
            gesture_widget.run_signal.connect(self._on_gesture_run_signal)
            gesture_widget.delete_signal.connect(self._on_gesture_delete_signal)
        self.functional_stack_widget.addWidget(self.gesture_tab_widget)

        # 设置界面
        self.settings_widget = SettingsPanelWidget()
        self.functional_stack_widget.addWidget(self.settings_widget)  # todo 这里会导致当urdf界面宽度变化时候， 整体软件宽度不对

        main_widget = QWidget()
        main_widget.setLayout(QHBoxLayout())
        main_widget.layout().setContentsMargins(26, 30, 100, 30)
        main_widget.layout().addWidget(hand_sim_viz_parent_widget)
        main_widget.layout().addWidget(self.functional_stack_widget)

        self.main_stack_widget.addWidget(main_widget)

        # 不再默认添加Hand Control标签页，而是在设备连接成功后添加
        # self.add_tab(self.tr("Hand Control"), False)

        # 创建初始化遮罩层
        self.initialization_overlay = InitializationOverlayWidget(self)
        self.initialization_overlay.hide()
        
    def _show_initialization_overlay(self):
        # 清空之前的任务并注册新任务
        self.initialization_overlay.clear_tasks()

        # 注册所有初始化任务（按照SDK中get_all_device_info_for_ui的顺序）
        self.initialization_overlay.register_task("Connecting to Device")
        self.initialization_overlay.register_task("Retrieving Control Unit")
        self.initialization_overlay.register_task("Getting Device Info")
        self.initialization_overlay.register_task("Getting Auto Calibration Status")
        self.initialization_overlay.register_task("Getting Turbo Mode Status")
        self.initialization_overlay.register_task("Getting LED Status")
        self.initialization_overlay.register_task("Getting Buzzer Status")
        self.initialization_overlay.register_task("Getting Vibration Status")
        self.initialization_overlay.register_task("Getting Finger Min Positions")
        self.initialization_overlay.register_task("Getting Finger Max Positions")
        self.initialization_overlay.register_task("Getting Finger Max Speeds")
        self.initialization_overlay.register_task("Getting Finger Max Currents")
        self.initialization_overlay.register_task("Getting Action Sequences")

        # 显示遮罩层
        self.initialization_overlay.setGeometry(self.rect())
        self.initialization_overlay.show()
        self.initialization_overlay.raise_()
        
    def _on_initialization_completed(self):
        global _FIRST_INIT_UI
        if _FIRST_INIT_UI:
            self.add_tab(self.tr("Hand Control"), False)
            self.add_tab(self.tr("Gesture"), False)
            _FIRST_INIT_UI = False
            self.main_stack_widget.setCurrentIndex(1)
            self.switch_tab(self.tr("Hand Control"))

        self._initialization_completed = True
        self.initialization_overlay.hide()
        
    def resizeEvent(self, event):
        super().resizeEvent(event)
        if hasattr(self, 'initialization_overlay') and self.initialization_overlay.isVisible():
            self.initialization_overlay.setGeometry(self.rect())

    def _start_plot_widget(self):
        hand_types = self.list_available_hand_types()
        self.finger_data_display_widget.update_plot(hand_types, self._buffers.copy())

    def _on_gesture_download_signal(self, hand_type, action_id, action_sequence_list):
        if len(action_sequence_list):
            self._sdk_manager.transfer_action_sequence(hand_type, ActionSequenceId(action_id), action_sequence_list)

    def _on_gesture_run_signal(self, hand_type, action_id):
        self._sdk_manager.run_action_sequence(hand_type, ActionSequenceId(action_id))

    def _on_gesture_delete_signal(self, hand_type, action_id):
        self._sdk_manager.transfer_action_sequence(hand_type, ActionSequenceId(action_id), [])

    def _on_home_button_clicked(self):
        url = QUrl(OFFICIAL_WEBSITE_ADDRESS)
        QDesktopServices.openUrl(url)

    def on_control_type_button_clicked(self, button):
        control_type = button.property('control_type')
        self.hand_control_widget.set_finger_controls_enabled_by_typed(control_type)

    def _on_tab_changed(self, index):
        self.functional_stack_widget.setCurrentIndex(index)

        is_control_visible = self.functional_stack_widget.currentWidget() == self.control_widget
        self.hand_sim_viz_widget.set_both_button_visible(is_control_visible)

        is_display_visible = self.finger_data_display_widget.isVisible()
        self._set_plot_timer_enabled(is_display_visible)

        # 根据界面可见性控制状态监控
        self._set_status_monitoring_enabled(is_display_visible)

        self._sdk_manager.clear_all_device_command_queues()

    def _on_tab_closed(self, index):
        logger.debug(f"标签关闭: index={index}")

    def _on_sdk_motor_status_received(self, status, hand_type):
        data_sources = [status.positions, status.speeds, status.currents]
        buffer_types = ['position', 'speed', 'current']
        
        # 初始化当前手的状态表格数据
        self._buffers[hand_type]['status'] = [[0.0] * 6 for _ in range(3)]
        
        # 更新数据
        for data_idx, (buffer_type, data_group) in enumerate(zip(buffer_types, data_sources)):
            for motor_idx, raw_value in enumerate(data_group):
                value = raw_value / 10
                
                # 更新缓冲区
                self._buffers[hand_type][buffer_type][motor_idx] = np.append(
                    self._buffers[hand_type][buffer_type][motor_idx], value)
                
                # 限制缓冲区大小
                if len(self._buffers[hand_type][buffer_type][motor_idx]) > self._max_plot_size:
                    self._buffers[hand_type][buffer_type][motor_idx] = self._buffers[hand_type][buffer_type][motor_idx][-self._max_plot_size:]
                
                # 更新状态表格数据
                self._buffers[hand_type]['status'][data_idx][motor_idx] = value

    def _on_sdk_connect_result(self, connected, port, hand_type, msg):
        logger.info(f"设备连接状态: {port}, 连接状态: {connected}, MSG: {msg}")

        if connected:
            self._sdk_manager.get_finger_unit_mode(hand_type)
        else:
            # 如果初始化遮罩层正在显示，标记连接任务失败
            if hasattr(self, 'initialization_overlay') and self.initialization_overlay.isVisible():
                self.initialization_overlay.fail_task("Connecting to Device", msg)
                # 即使连接失败，也可以继续尝试其他任务（如果需要的话）

            window = AlertWidget(self)
            window.set_image(get_image("alert.svg"))
            window.set_title(self.tr("Connect Error"))
            window.set_message(msg)
            window.set_button_text(self.tr("OK"))
            window.show()

    def _on_sdk_finger_unit_mode_received(self, unit_mode, hand_type):
        # 设置当前程序允许连接的手版本类型
        self._expected_hand_version = self._sdk_manager._hands[hand_type].config.hand_version

        # 显示初始化遮罩层
        self._show_initialization_overlay()

        # 开始执行初始化任务
        if hasattr(self, 'initialization_overlay'):
            # 标记设备连接和控制单元任务完成
            QTimer.singleShot(100, lambda: self.initialization_overlay.complete_task("Connecting to Device"))
            QTimer.singleShot(200, lambda: self.initialization_overlay.complete_task("Retrieving Control Unit"))

            # 开始获取设备信息的各个子任务
            self._sdk_manager.get_all_device_info_for_ui(hand_type)
    
    def _on_sdk_get_completed_device_info(self, hand_device: HandDevice):
        logger.info(f"当前连接手信息: \n{hand_device.config}")
        # 切换URDF显示界面左右手信息
        self.hand_sim_viz_widget.switch_hand_type_button("Left" if hand_device.config.hand_type == HandType.Left else "Right")

        # 切换显示urdf模型
        num_hands = len(list(self._sdk_manager._hands.keys()))
        if num_hands == 2:
            self.hand_sim_viz_widget.set_cur_hand_tab_widget_visible("Both")
        elif num_hands == 1:
            hand_type = hand_device.config.hand_type
            self.hand_sim_viz_widget.set_cur_hand_tab_widget_visible("Left" if hand_type == HandType.Left else "Right")
        else:
            raise ValueError("Unexpected number of hands")

        # 更新SDK HandDevice Name
        hand_device.config.name = get_config(f"Hands.{hand_device.config.hand_type.name}.name")

        # 通知初始化遮罩层设备信息获取完成
        if hasattr(self, 'initialization_overlay') and self.initialization_overlay.isVisible():
            self.initialization_overlay.on_device_info_completed()

        # 切换控制单位按钮
        self.finger_data_display_widget.radioButton_percentage.clicked.disconnect(self._on_radio_button_percentage_clicked)
        self.finger_data_display_widget.radioButton_number.clicked.disconnect(self._on_radio_button_percentage_clicked)
        self.finger_data_display_widget.radioButton_number.setChecked(hand_device.config.unit_mode == stark.FingerUnitMode.Physical)
        self.finger_data_display_widget.radioButton_percentage.clicked.connect(self._on_radio_button_percentage_clicked)
        self.finger_data_display_widget.radioButton_number.clicked.connect(self._on_radio_button_percentage_clicked)

        self._set_settings_widget_params(hand_device.config.hand_type)

        # 设置输入框范围
        min_positions = [0, 0, 0, 0, 0, 0]
        max_positions = [100, 100, 100, 100, 100, 100]
        min_speeds = [-100, -100, -100, -100, -100, -100]
        max_speeds = [100, 100, 100, 100, 100, 100]
        min_currents = [-100, -100, -100, -100, -100, -100]
        max_currents = [100, 100, 100, 100, 100, 100]
        self._input_field_range[hand_device.config.hand_type]["normalized"] = [min_positions, max_positions, min_speeds, max_speeds, min_currents, max_currents]
        min_positions = hand_device.config.min_positions
        max_positions = hand_device.config.max_positions
        min_speeds = [-i for i in hand_device.config.max_speeds]
        max_speeds = hand_device.config.max_speeds
        min_currents = [-i for i in hand_device.config.max_currents]
        max_currents = hand_device.config.max_currents
        self._input_field_range[hand_device.config.hand_type]["physical"] = [min_positions, max_positions, min_speeds, max_speeds, min_currents, max_currents]
        unit_mode_str = "physical" if hand_device.config.unit_mode == stark.FingerUnitMode.Physical else "normalized"
        self.hand_control_widget.set_input_field_range(*self._input_field_range[hand_device.config.hand_type][unit_mode_str])

        # 更新手势信息
        self.gesture_tab_widget.currentWidget().gesture_manager.load_data(hand_device.config.serial_number, hand_device.config.get_action_sequences())
        self.gesture_tab_widget.currentWidget().load_custom_gestures()
        self.gesture_tab_widget.currentWidget().set_input_field_range(*self._input_field_range[hand_device.config.hand_type]["physical"])

        is_control_visible = self.functional_stack_widget.currentWidget() == self.control_widget
        self._set_plot_timer_enabled(is_control_visible)

        # 根据当前界面状态决定是否启动状态监控
        is_display_visible = self.finger_data_display_widget.isVisible()
        if is_display_visible:
            self._sdk_manager.start_motor_status_monitoring(hand_device.config.hand_type)

    def _on_initialization_task_completed(self, task_name: str, hand_type):
        """处理初始化任务完成"""
        if hasattr(self, 'initialization_overlay') and self.initialization_overlay.isVisible():
            self.initialization_overlay.complete_task(task_name)

    def _set_plot_timer_enabled(self, enabled):
        if enabled:
            if self._plot_timer.isActive():
                return
            self._plot_timer.start(DRAW_REFRESH_PERIOD)
        else:
            if not self._plot_timer.isActive():
                return
            self._plot_timer.stop()

    def _set_status_monitoring_enabled(self, enabled):
        hand_types = self.list_available_hand_types()
        for hand_type in hand_types:
            if hand_type in self._sdk_manager._hands:
                if enabled:
                    self._sdk_manager.start_motor_status_monitoring(hand_type)
                else:
                    self._sdk_manager.stop_motor_status_monitoring(hand_type)

    def _set_settings_widget_params(self, hand_type: HandType):
        hand_device = self._sdk_manager._hands[hand_type]

        # 设置device_name
        left_device_name = get_config("Hands.Left.name")
        right_device_name = get_config("Hands.Right.name")
        self.settings_widget.device_name_lineedit.setText(
            left_device_name if hand_device.config.hand_type == HandType.Left else right_device_name)

        # 将默认参数提交到设置界面
        self.settings_widget.id_widget.set_value(hand_device.config.slave_address)
        self.settings_widget.baudrate_widget.set_value(hand_device.config.connect_rs485_baudrate.name[4:])
        self.settings_widget.protocol_name_widget.set_value(hand_device.config.protocol_type.name.upper())
        self.settings_widget.sku_widget.set_value(hand_device.config.sku)
        self.settings_widget.sn_widget.set_value(hand_device.config.serial_number)
        self.settings_widget.fw_version_widget.set_value(hand_device.config.firmware_version)

        # 设置手指参数的界面
        self.settings_widget.enable_auto_zeroing_widget.setChecked(hand_device.config.auto_calibration_enabled)
        self.settings_widget.enabl_turbo_widget.setChecked(hand_device.config.turbo_mode_enabled)
        self.settings_widget.enabl_led_widget.setChecked(hand_device.config.led_enabled)
        self.settings_widget.enabl_buzzer_widget.setChecked(hand_device.config.buzzer_enabled)
        self.settings_widget.enabl_vibration_widget.setChecked(hand_device.config.vibration_enabled)
        self.settings_widget.min_position_input_widget.setText(",".join(str(x) for x in hand_device.config.min_positions))
        self.settings_widget.max_position_input_widget.setText(",".join(str(x) for x in hand_device.config.max_positions))
        self.settings_widget.max_speed_input_widget.setText(",".join(str(x) for x in hand_device.config.max_speeds))
        self.settings_widget.max_force_input_widget.setText(",".join(str(x) for x in hand_device.config.max_currents))

    def _on_sdk_error_occurred(self, error_msg, hand_type):
        logger.error(f"{hand_type.name}错误: {error_msg}")
        window = AlertWidget(self)
        window.set_image(get_image("alert.svg"))
        window.set_title(self.tr("Connect Error"))
        window.set_message(error_msg)
        window.set_button_text(self.tr("OK"))
        window.show()

    def _on_sdk_dfu_error(self, hand_type, error_msg):
        self.ota_dialog.close_dialog()

        logger.error(f"DFU错误: {error_msg}")
        window = AlertWidget(self)
        window.set_image(get_image("alert.svg"))
        window.set_title(self.tr("OTA Error"))
        window.set_message(error_msg)
        window.set_button_text(self.tr("OK"))
        window.show()

    def _on_sdk_finger_unit_mode_error(self, error_msg):
        # 如果初始化遮罩层正在显示，标记获取控制单元任务失败
        if hasattr(self, 'initialization_overlay') and self.initialization_overlay.isVisible():
            self.initialization_overlay.fail_task("Retrieving Control Unit", error_msg)

        window = AlertWidget(self)
        window.set_image(get_image("alert.svg"))
        window.set_title(self.tr("Connect Error"))
        window.set_message(self.tr("""Please verify the Port, ID, and Baudrate settings.\n
        Defaults: Left hand – ID: 126, Baudrate: 460800; Right hand – ID: 127, Baudrate: 460800.
        To restore defaults, press and hold the power button."""))
        window.set_button_text(self.tr("OK"))
        window.show()

    def _on_device_connect_signal(self, connect_config):
        if connect_config:
            hand_config = HandConfig()
            hand_config.hand_version = connect_config["hand_version"]
            hand_config.protocol_type = connect_config["protocol_type"]
            hand_config.connect_port = connect_config["port"]
            hand_config.connect_rs485_baudrate = Baudrate(connect_config["baudrate"])
            hand_config.slave_address = connect_config["slave_address"]

            self._sdk_manager.connect_device(hand_config)

        else:
            logger.error(f"connect_config is None: {connect_config}")
            sys.exit(0)

    def _on_settings_button_clicked(self):
            
        # 添加设置标签页
        self.add_tab(self.tr("Settings"), True)
        
        # 显式地切换到设置标签页
        self.switch_tab(self.tr("Settings"))
        
        # 更新功能状态
        self.functional_stack_widget.setCurrentIndex(2)
        self.hand_sim_viz_widget.set_both_button_visible(False)
        
        # If currently in "Both" mode, switch to a single hand
        if self.hand_sim_viz_widget.current_hand_type() == "Both":
            hand_types = list(self._sdk_manager._hands.values())
            if hand_types:
                first_hand_type = hand_types[0]
                self.hand_sim_viz_widget.switch_hand_type_signal.emit("Left" if first_hand_type.config.hand_type == HandType.Left else "Right")

    def _on_device_name_changed(self, text):
        hand_types = self.list_available_hand_types()
        assert len(hand_types) == 1
        hand_type = hand_types[0]
        self._sdk_manager._hands[hand_type].config.name = text

        if hand_type == HandType.Left:
            self.hand_sim_viz_widget.label_name_left.setText(text)
        else:
            self.hand_sim_viz_widget.label_name_right.setText(text)

    def _on_upgrade_fw_widget_clicked(self):
        file = QFileDialog.getOpenFileName(self, "File", "", "bin (*.bin)")
        if file[0]:
            hand_types = self.list_available_hand_types()
            assert len(hand_types) == 1

            self._sdk_manager.start_dfu(hand_type=hand_types[0],
                                        dfu_file_path=file[0])

    def _on_slider_value_changed(self, finger_id, value):
        hand_types = self.list_available_hand_types()
        for hand_type in hand_types:
            self._sdk_manager.set_finger_position_with_millis(hand_type, finger_id, value * 10, 1)

    def _on_change_hand_type(self, hand_type_str):
        self.hand_sim_viz_widget.switch_hand_type_button(hand_type_str)

        if hand_type_str == "Both":
            self.hand_sim_viz_widget.set_cur_hand_tab_widget_visible("Both")
            self.finger_data_display_widget.set_extra_widget_visible(True)
            self.gesture_tab_widget.setEnabled(False)
        else:
            hand_type_enum = HandType.Left if hand_type_str == "Left" else HandType.Right

            self.hand_sim_viz_widget.set_cur_hand_tab_widget_visible(hand_type_str)

            # 切换手势页面
            gesture_tab_index = 0 if hand_type_enum == HandType.Left else 1
            self.gesture_tab_widget.setEnabled(True)
            self.gesture_tab_widget.setCurrentIndex(gesture_tab_index)
            # 设置手势页面是否可编辑
            is_hand_available = hand_type_enum in self._sdk_manager._hands
            self.gesture_tab_widget.widget(gesture_tab_index).setEnabled(is_hand_available)

            self.finger_data_display_widget.set_extra_widget_visible(False)

            if hand_type_enum in self._sdk_manager._hands.keys():
                self.settings_widget.setEnabled(True)

                self._set_settings_widget_params(hand_type_enum)

                if hand_type_enum == HandType.Left:
                    self.hand_sim_viz_widget.tabWidget_model_left.setCurrentIndex(0)
                else:
                    self.hand_sim_viz_widget.tabWidget_model_right.setCurrentIndex(0)

                # 同步设置对应手的控制单位
                cur_unit_mode = self._sdk_manager._hands[hand_type_enum].config.unit_mode
                self.finger_data_display_widget.radioButton_percentage.clicked.disconnect(
                    self._on_radio_button_percentage_clicked)
                self.finger_data_display_widget.radioButton_number.clicked.disconnect(
                    self._on_radio_button_percentage_clicked)
                self.finger_data_display_widget.radioButton_number.setChecked(
                    cur_unit_mode == stark.FingerUnitMode.Physical)
                self.finger_data_display_widget.radioButton_percentage.setChecked(
                    cur_unit_mode == stark.FingerUnitMode.Normalized)
                self.finger_data_display_widget.radioButton_percentage.clicked.connect(
                    self._on_radio_button_percentage_clicked)
                self.finger_data_display_widget.radioButton_number.clicked.connect(
                    self._on_radio_button_percentage_clicked)

                # 切换动作序列到对应的手势页面
                self.gesture_tab_widget.setCurrentIndex(0 if hand_type_enum == HandType.Left else 1)

            else:
                self.settings_widget.setEnabled(False)

                # 当前手类型未连接。
                if hand_type_enum == HandType.Left:
                    self.hand_sim_viz_widget.tabWidget_model_left.setCurrentIndex(1)
                else:
                    self.hand_sim_viz_widget.tabWidget_model_right.setCurrentIndex(1)

    def _on_urdf_connect_button_clicked(self, hand_type: HandType):
        dialog = DeviceConnectDialog(self)
        dialog.connect_signal.connect(self._on_device_connect_signal)
        dialog.show_dialog(self._expected_hand_version, hand_type)

    def _on_radio_button_percentage_clicked(self):
        hand_types = self.list_available_hand_types()
        for hand_type in hand_types:
            if self.sender() == self.finger_data_display_widget.radioButton_number:
                mode = stark.FingerUnitMode.Physical
                mode_str = "physical"
            else:
                mode = stark.FingerUnitMode.Normalized
                mode_str = "normalized"

            self._sdk_manager.set_finger_unit_mode(hand_type, mode)

            self.hand_control_widget.set_input_field_range(*self._input_field_range[hand_type][mode_str])

    def _on_set_packing_gestures_button_clicked(self):
        hand_types = self.list_available_hand_types()
        for hand_type in hand_types:
            self._sdk_manager.set_packing_gestures(hand_type)

    def _on_set_gesture_zero_button_clicked(self):
        hand_types = self.list_available_hand_types()
        for hand_type in hand_types:
            self._sdk_manager.calibrate_position(hand_type)

    def _on_apply_all_button_clicked(self):
        unit_mode = stark.FingerUnitMode.Normalized if self.finger_data_display_widget.radioButton_percentage.isChecked() else stark.FingerUnitMode.Physical

        controls = [
            self.hand_control_widget.thumb_1_control,
            self.hand_control_widget.thumb_2_control,
            self.hand_control_widget.index_control,
            self.hand_control_widget.middle_control,
            self.hand_control_widget.ring_control,
            self.hand_control_widget.pinky_control
        ]

        control_type = self.control_type_button_group.checkedButton().property("control_type")

        position_values = [control.doubleSpinBox_position.value() for control in controls]
        speed_values = [control.doubleSpinBox_speed.value() for control in controls]
        current_values = [control.doubleSpinBox_force.value() for control in controls]

        hand_types = self.list_available_hand_types()
        for hand_type in hand_types:
            if control_type == ControlType.POSITION:
                self._sdk_manager.set_finger_positions_and_durations(
                    hand_type,
                    convert_value_to_sdk_unit(unit_mode, position_values),
                    [1] * 6
                )
            elif control_type == ControlType.SPEED:
                self._sdk_manager.set_finger_speeds(
                    hand_type,
                    convert_value_to_sdk_unit(unit_mode, speed_values)
                )
            elif control_type == ControlType.FORCE:
                self._sdk_manager.set_finger_currents(
                    hand_type,
                    convert_value_to_sdk_unit(unit_mode, current_values)
                )
            elif control_type == ControlType.POSITION_SPEED:
                self._sdk_manager.set_finger_positions_and_speeds(
                    hand_type,
                    convert_value_to_sdk_unit(unit_mode, position_values),
                    convert_value_to_sdk_unit(unit_mode, speed_values)
                )

    def _on_dfu_state_callback(self, hand_type: HandType, state: stark.DfuState):
        logger.info(f"DFU STATE: {hand_type} {state}")
        for key, value in stark.DfuState.__dict__.items():
            if not key.startswith("__"):
                if state == value:
                    self.ota_dialog.update_state(key)

        # 注意：不在这里关闭对话框，而是在dfu_finished信号中处理

    def _on_dfu_progress_callback(self, hand_type: HandType, progress: float):
        self.ota_dialog.update_progress(progress)

    def _on_dfu_finished(self, hand_type: HandType, success: bool):
        logger.info(f"DFU升级完成: {hand_type.name}, 成功: {success}")

        # # 先不使用重连机制
        # self.ota_dialog.close_dialog()
        # # 切换连接页面
        # self._sdk_manager.disconnect_device(hand_type)
        # self.hand_sim_viz_widget.tabWidget_model.setCurrentIndex(1)

        if success:
            # 显示重新连接状态
            self.ota_dialog.update_state(self.tr("Upgrade successful. Reconnecting to the device...."))
        else:
            self.ota_dialog.update_state(self.tr("Upgrade failed. Attempting to reconnect to the device...."))

    def _on_dfu_reconnect(self, hand_type: HandType, success: bool, message: str):
        logger.info(f"DFU后重新连接结果: {hand_type.name}, 成功: {success}, 消息: {message}")

        self.ota_dialog.close_dialog()

        if success:
            # 重新获取设备信息以更新UI
            self._sdk_manager.get_finger_unit_mode(hand_type)
        else:
            # 重新连接失败，显示错误消息
            window = AlertWidget(self)
            window.set_image(get_image("alert.svg"))
            window.set_title(self.tr("Reconnection failed"))
            window.set_message(self.tr(f"After the firmware upgrade, the device cannot reconnect automatically。"
                                       f"\n{message}\n\nPlease connect the device manually."))
            window.set_button_text(self.tr("OK"))
            window.show()

    def _update_urdf_viewer(self, hand_type: HandType, finger_ids, positions):
        widget = self.hand_sim_viz_widget.left_urdf_viewer if hand_type == HandType.Left else self.hand_sim_viz_widget.right_urdf_viewer
        if len(finger_ids) == 1:
            widget.finger_control(finger_ids[0].name.lower(), positions[0])
        else:
            widget.fingers_control([i.name.lower() for i in finger_ids], positions)

    def list_available_hand_types(self):
        current_hand_type = self.hand_sim_viz_widget.current_hand_type().lower()
        if current_hand_type in ["left", "right"]:
            hand_types = [HandType.Left if current_hand_type == "left" else HandType.Right]
        else:
            hand_types = [HandType.Left, HandType.Right]
        return hand_types

    def closeEvent(self, event):
        self._plot_timer.stop()

        # 停止所有状态监控
        for hand_type in list(self._sdk_manager._hands.keys()):
            self._sdk_manager.stop_motor_status_monitoring(hand_type)

        time.sleep(0.1)

        # 提取需要保存的配置信息
        need_save_config = {}
        for hand_device in self._sdk_manager._hands.values():
            need_save_config[hand_device.config.hand_type] = hand_device.config

        self._sdk_manager.close()

        # 保存配置文件
        main_config = get_config("All")
        for hand_type, config in need_save_config.items():
            main_config["Hands"][hand_type.name]["name"] = config.name
        save_config(main_config)

        super().closeEvent(event)


# 转换值为sdk控制单位值
def convert_value_to_sdk_unit(unit_mode: stark.FingerUnitMode, data):
    if unit_mode != stark.FingerUnitMode.Normalized:
        return [int(value) for value in data]

    if isinstance(data, list):
        return [int(value * 10) for value in data]
    else:
        return int(data * 10)

